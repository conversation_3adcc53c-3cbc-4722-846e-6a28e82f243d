<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:51:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-31 13:52:14
 * @FilePath     : /src/components/searchGame/SearchBar.vue
 * @Description  : 搜索栏组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:51:00
-->

<template>
    <div class="search-bar-container">
        <div class="search-bar" :class="{ focused: isInputFocused }" @click="handleSearchBarClick">
            <!-- 分类选择器 -->
            <div class="category-selector" @click.stop="handleCategoryClick">
                <span class="category-text">{{ selectedCategoryText }}</span>
                <van-icon name="arrow-down" class="category-icon" />
            </div>

            <!-- 搜索输入框 -->
            <div class="search-field-wrapper">
                <input
                    ref="inputRef"
                    v-model="searchKeyword"
                    type="text"
                    placeholder="Game Name"
                    class="search-input"
                    @input="handleInputChange"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @keyup.enter="handleSearch"
                />
                <!-- 清空按钮 -->
                <div v-if="searchKeyword" class="clear-btn" @click.stop="clearInput">
                    <van-icon name="cross" class="clear-icon" />
                </div>
            </div>

            <!-- 搜索按钮 -->
            <div class="search-icon-wrapper" @click.stop="handleSearch">
                <van-icon name="search" class="search-icon" />
            </div>
        </div>

        <!-- 搜索历史和推荐 -->
        <SearchHistory
            ref="searchHistoryRef"
            v-if="showSearchHistory"
            :visible="showSearchHistory"
            :keyword="searchKeyword"
            @select-history="onSelectHistory"
            @select-game="onSelectGame"
        />
    </div>

    <!-- 分类选择弹窗 -->
    <SelectSheet
        v-model:show="showCategorySheet"
        title="Select"
        :options="categoryOptions"
        :selected-value="selectedCategoryText"
        custom-class="category-action-sheet"
        @select="onCategorySelect"
    />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import SelectSheet from './SelectSheet.vue'
import SearchHistory from './SearchHistory.vue'

// 定义属性
interface Props {
    modelValue?: string
    selectedCategory?: string
}

// 定义事件
interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
    (e: 'category-click'): void
    (e: 'update:selectedCategory', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    selectedCategory: 'Casino',
})

const emit = defineEmits<Emits>()

const searchKeyword = ref(props.modelValue)
const showCategorySheet = ref(false)
const selectedCategoryText = ref(props.selectedCategory)
const showSearchHistory = ref(false)
const searchHistoryRef = ref<InstanceType<typeof SearchHistory> | null>(null)
const isInputFocused = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

// 分类选项
const categoryOptions = [
    { name: 'Casino', value: 'Casino' },
    { name: 'Sports', value: 'Sports' },
    { name: 'Live Casino', value: 'Live Casino' },
    { name: 'Slots', value: 'Slots' },
    { name: 'Lottery', value: 'Lottery' },
]

const clearInput = () => {
    searchKeyword.value = ''
    emit('update:modelValue', '')
    // 清空后显示搜索历史面板
    showSearchHistory.value = true
}

const handleSearch = () => {
    // 执行搜索时添加到历史
    if (searchKeyword.value.trim().length > 0) {
        searchHistoryRef.value?.addSearchHistory(searchKeyword.value)
    }
    emit('search', searchKeyword.value)
    showSearchHistory.value = false
}

const handleCategoryClick = () => {
    showCategorySheet.value = true
    // emit('category-click')
}

const handleInputChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    emit('update:modelValue', value)

    // 当输入长度>=3时，隐藏搜索历史面板
    if (value && value.length >= 3) {
        showSearchHistory.value = false
    } else if (document.activeElement === target) {
        // 当输入框有焦点且输入长度<3时，显示搜索历史面板
        showSearchHistory.value = true
    }
}

// 处理输入框获得焦点
const handleFocus = () => {
    console.log('handleFocus called, setting isInputFocused to true')
    isInputFocused.value = true
    console.log('isInputFocused.value is now:', isInputFocused.value)

    // 检查 DOM 元素的类名
    nextTick(() => {
        const searchBar = document.querySelector('.search-bar')
        console.log('After focus, search-bar classes:', searchBar?.className)
    })

    // 仅当输入长度<3时显示搜索历史面板
    if (!searchKeyword.value || searchKeyword.value.length < 3) {
        showSearchHistory.value = true
    }
}

// 处理输入框失去焦点
const handleBlur = () => {
    console.log('handleBlur called, setting isInputFocused to false')
    isInputFocused.value = false
    console.log('isInputFocused.value is now:', isInputFocused.value)

    // 检查 DOM 元素的类名
    nextTick(() => {
        const searchBar = document.querySelector('.search-bar')
        console.log('After blur, search-bar classes:', searchBar?.className)
    })
}

// 处理搜索栏点击事件
const handleSearchBarClick = () => {
    console.log('handleSearchBarClick called, current isInputFocused:', isInputFocused.value)
    // 使用 nextTick 确保在下一个事件循环中执行
    nextTick(() => {
        console.log('In nextTick, isInputFocused:', isInputFocused.value)
        // 只有当输入框已聚焦时才执行失焦操作
        if (isInputFocused.value && inputRef.value) {
            console.log('Input is focused, calling blur()')
            inputRef.value.blur()
        }
    })
}

// 选择历史记录
const onSelectHistory = (keyword: string) => {
    searchKeyword.value = keyword
    emit('update:modelValue', keyword)
    showSearchHistory.value = false
}

// 选择游戏
const onSelectGame = (game: any) => {
    searchKeyword.value = game.name
    emit('update:modelValue', game.name)
    showSearchHistory.value = false
}

// 处理分类选择
const onCategorySelect = (option: { name: string; value: string }) => {
    selectedCategoryText.value = option.name
    emit('update:selectedCategory', option.value)
}

// 点击外部关闭搜索历史
const handleClickOutside = (event: MouseEvent) => {
    const searchBarContainer = document.querySelector('.search-bar-container')
    if (searchBarContainer && !searchBarContainer.contains(event.target as Node)) {
        showSearchHistory.value = false
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
})

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        searchKeyword.value = newValue
    }
)

// 监听searchKeyword变化，控制搜索历史面板显示
watch(
    () => searchKeyword.value,
    (newValue) => {
        // 当输入长度>=3时，隐藏搜索历史面板
        if (newValue && newValue.length >= 3) {
            showSearchHistory.value = false
        }
    }
)

// 监听selectedCategory变化
watch(
    () => props.selectedCategory,
    (newValue) => {
        // 找到对应的选项获取名称
        const option = categoryOptions.find((opt) => opt.value === newValue)
        if (option) {
            selectedCategoryText.value = option.name
        } else {
            selectedCategoryText.value = newValue || 'Casino'
        }
    }
)
</script>

<style lang="scss" scoped>
$bg-color: #2c3031;
$text-primary: #ffffff;
$text-placeholder: rgba(255, 255, 255, 0.6);
$category-bg: rgba(255, 255, 255, 0.1);
$input-bg: rgba(255, 255, 255, 0.08);
$icon-color: rgba(255, 255, 255, 0.8);

.search-bar-container {
    position: relative;
    z-index: 100;
}

.search-bar {
    margin: 20px 10px 0 10px;
    padding: 12px 16px;
    background: $bg-color;
    flex-shrink: 0;
    height: 80px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: $input-bg;
    border: 1px solid #ffffff0d;
    transition: border-color 0.2s ease;

    &.focused {
        border-color: #2cee88 !important;
        box-shadow: 0 0 0 1px #2cee88;
    }

    // 分类选择器样式
    .category-selector {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 20px 0 10px;
        margin-left: 8px;
        min-width: 80px;
        border-right: 1px solid #e5e7eb;

        .category-text {
            color: $text-primary;
            font-size: 26px;
            font-weight: 500;
        }

        .category-icon {
            color: $icon-color;
            font-size: 26px;
            margin-left: 10px;
        }
    }

    // 搜索输入框样式
    .search-field-wrapper {
        flex: 1;
        height: 100%;
        margin: 0 10px;
        display: flex;
        align-items: center;
        position: relative;

        .search-input {
            width: 100%;
            height: 70%;
            background: transparent;
            border: none;
            outline: none;
            color: $text-primary;
            font-size: 26px;
            padding: 0;
            padding-right: 40px; // 为清空按钮留出空间

            &::placeholder {
                color: $text-placeholder;
            }
        }

        .clear-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #ffffff1a;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .clear-icon {
                color: $text-primary;
                font-size: 18px;
            }
        }
    }

    // 搜索图标样式
    .search-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 12px;
        cursor: pointer;

        .search-icon {
            color: $icon-color;
            font-size: 36px;
        }
    }
}
</style>
